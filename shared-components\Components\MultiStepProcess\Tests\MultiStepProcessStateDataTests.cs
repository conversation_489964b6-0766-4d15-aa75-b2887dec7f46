using Microsoft.VisualStudio.TestTools.UnitTesting;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Interfaces;
using System.Text.Json;

namespace shared.Components.MultiStepProcess.Tests
{
    [TestClass]
    public class MultiStepProcessStateDataTests
    {
        // Test state enum
        public enum TestState
        {
            Initial,
            Processing,
            Completed
        }

        // Test object implementing IStateful
        public class TestObject : IStateful<TestState>
        {
            public string Id { get; set; } = Guid.NewGuid().ToString();
            public TestState Status { get; set; } = TestState.Initial;
            public string Data { get; set; } = string.Empty;
        }

        [TestMethod]
        public void Constructor_WithValidObject_ShouldInitializeCorrectly()
        {
            // Arrange
            var testObject = new TestObject { Id = "test-123", Data = "test data" };
            var correlationId = "correlation-456";

            // Act
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject, correlationId);

            // Assert
            Assert.AreEqual(testObject, stateData.StatefulObject);
            Assert.AreEqual(testObject.Status, stateData.CurrentState);
            Assert.AreEqual(correlationId, stateData.CorrelationId);
            Assert.AreEqual(MultiStepProcessState.Running, stateData.ProcessState);
            Assert.AreEqual(0, stateData.RetryCount);
            Assert.IsFalse(stateData.IsFinished);
            Assert.AreEqual(MultiStepProcessFailureReason.None, stateData.FailureReason);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Constructor_WithNullObject_ShouldThrowException()
        {
            // Act
            new MultiStepProcessStateData<TestState, TestObject>(null);
        }

        [TestMethod]
        public void ParameterlessConstructor_ShouldCreateInstance()
        {
            // Act
            var stateData = new MultiStepProcessStateData<TestState, TestObject>();

            // Assert
            Assert.IsNotNull(stateData);
            // Note: StatefulObject will be null with parameterless constructor
        }

        [TestMethod]
        public void UpdateProcessState_ShouldUpdateStateAndTimestamp()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);
            var originalTimestamp = stateData.UpdatedAt;

            // Wait a bit to ensure timestamp difference
            Thread.Sleep(10);

            // Act
            stateData.UpdateProcessState(MultiStepProcessState.RollingBack);

            // Assert
            Assert.AreEqual(MultiStepProcessState.RollingBack, stateData.ProcessState);
            Assert.IsTrue(stateData.UpdatedAt > originalTimestamp);
        }

        [TestMethod]
        public void IncrementRetryCount_ShouldIncrementAndUpdateTimestamp()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);
            var originalTimestamp = stateData.UpdatedAt;

            // Wait a bit to ensure timestamp difference
            Thread.Sleep(10);

            // Act
            stateData.IncrementRetryCount();

            // Assert
            Assert.AreEqual(1, stateData.RetryCount);
            Assert.IsTrue(stateData.UpdatedAt > originalTimestamp);
        }

        [TestMethod]
        public void ResetRetryCount_ShouldResetToZeroAndUpdateTimestamp()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);
            stateData.IncrementRetryCount();
            stateData.IncrementRetryCount();
            var originalTimestamp = stateData.UpdatedAt;

            // Wait a bit to ensure timestamp difference
            Thread.Sleep(10);

            // Act
            stateData.ResetRetryCount();

            // Assert
            Assert.AreEqual(0, stateData.RetryCount);
            Assert.IsTrue(stateData.UpdatedAt > originalTimestamp);
        }

        [TestMethod]
        public void MarkAsFinished_WithoutFailureReason_ShouldMarkFinishedWithNone()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);
            var originalTimestamp = stateData.UpdatedAt;

            // Wait a bit to ensure timestamp difference
            Thread.Sleep(10);

            // Act
            stateData.MarkAsFinished();

            // Assert
            Assert.IsTrue(stateData.IsFinished);
            Assert.AreEqual(MultiStepProcessFailureReason.None, stateData.FailureReason);
            Assert.IsTrue(stateData.UpdatedAt > originalTimestamp);
        }

        [TestMethod]
        public void MarkAsFinished_WithFailureReason_ShouldMarkFinishedWithReason()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);

            // Act
            stateData.MarkAsFinished(MultiStepProcessFailureReason.RetryLimitExceeded);

            // Assert
            Assert.IsTrue(stateData.IsFinished);
            Assert.AreEqual(MultiStepProcessFailureReason.RetryLimitExceeded, stateData.FailureReason);
        }

        [TestMethod]
        public void IsRetryLimitExceeded_WithCountBelowLimit_ShouldReturnFalse()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);
            stateData.IncrementRetryCount(); // Count = 1

            // Act
            var result = stateData.IsRetryLimitExceeded(3);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsRetryLimitExceeded_WithCountAtLimit_ShouldReturnTrue()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);
            stateData.IncrementRetryCount();
            stateData.IncrementRetryCount();
            stateData.IncrementRetryCount(); // Count = 3

            // Act
            var result = stateData.IsRetryLimitExceeded(3);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void IsRetryLimitExceeded_WithCountAboveLimit_ShouldReturnTrue()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);
            for (int i = 0; i < 5; i++)
            {
                stateData.IncrementRetryCount(); // Count = 5
            }

            // Act
            var result = stateData.IsRetryLimitExceeded(3);

            // Assert
            Assert.IsTrue(result);
        }

        [TestMethod]
        public void CurrentState_ShouldReturnStatefulObjectStatus()
        {
            // Arrange
            var testObject = new TestObject { Status = TestState.Processing };
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);

            // Act
            var currentState = stateData.CurrentState;

            // Assert
            Assert.AreEqual(TestState.Processing, currentState);
        }



        [TestMethod]
        public void Serialization_ShouldPreserveAllData()
        {
            // Arrange
            var testObject = new TestObject 
            { 
                Id = "test-123", 
                Status = TestState.Processing, 
                Data = "important data" 
            };
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject, "correlation-456");
            stateData.UpdateProcessState(MultiStepProcessState.RollingBack);
            stateData.IncrementRetryCount();
            stateData.IncrementRetryCount();

            // Act
            var json = JsonSerializer.Serialize(stateData);
            var deserializedStateData = JsonSerializer.Deserialize<MultiStepProcessStateData<TestState, TestObject>>(json);

            // Assert
            Assert.IsNotNull(deserializedStateData);
            Assert.AreEqual("test-123", deserializedStateData.StatefulObject.Id);
            Assert.AreEqual(TestState.Processing, deserializedStateData.StatefulObject.Status);
            Assert.AreEqual("important data", deserializedStateData.StatefulObject.Data);
            Assert.AreEqual("correlation-456", deserializedStateData.CorrelationId);
            Assert.AreEqual(MultiStepProcessState.RollingBack, deserializedStateData.ProcessState);
            Assert.AreEqual(2, deserializedStateData.RetryCount);
            Assert.IsFalse(deserializedStateData.IsFinished);
            Assert.AreEqual(MultiStepProcessFailureReason.None, deserializedStateData.FailureReason);
        }

        [TestMethod]
        public void Serialization_WithFinishedState_ShouldPreserveFinishedData()
        {
            // Arrange
            var testObject = new TestObject();
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject);
            stateData.MarkAsFinished(MultiStepProcessFailureReason.Timeout);

            // Act
            var json = JsonSerializer.Serialize(stateData);
            var deserializedStateData = JsonSerializer.Deserialize<MultiStepProcessStateData<TestState, TestObject>>(json);

            // Assert
            Assert.IsNotNull(deserializedStateData);
            Assert.IsTrue(deserializedStateData.IsFinished);
            Assert.AreEqual(MultiStepProcessFailureReason.Timeout, deserializedStateData.FailureReason);
        }
    }
}
