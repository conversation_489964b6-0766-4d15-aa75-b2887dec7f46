using shared.Components.MultiStepProcess;
using shared.Components.MultiStepProcess.Builders;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.MultiStepProcess.Examples
{
    /// <summary>
    /// Example demonstrating step-level max retries functionality.
    /// Shows how different steps can have different retry limits.
    /// </summary>
    public static class StepLevelRetriesExample
    {
        public enum ProcessingState
        {
            Initial,
            NetworkCall,
            DatabaseUpdate,
            FileOperation,
            Completed
        }

        public class ProcessingTask : IStateful<ProcessingState>
        {
            public ProcessingState Status { get; set; } = ProcessingState.Initial;
            public List<string> Log { get; set; } = new List<string>();
            public int NetworkCallAttempts { get; set; } = 0;
            public int DatabaseUpdateAttempts { get; set; } = 0;
            public int FileOperationAttempts { get; set; } = 0;
        }

        /// <summary>
        /// Demonstrates step-level max retries with different retry counts for different types of operations.
        /// </summary>
        public static async Task<bool> RunExample()
        {
            Console.WriteLine("\n=== Step-Level Max Retries Example ===");

            var task = new ProcessingTask();

            // Create process with different retry strategies for different step types
            var process = new MultiStepProcessBuilder<ProcessingState, ProcessingTask>()
                // Network calls are unreliable - allow more retries
                .AddStep(ProcessingState.Initial, NetworkCallTask, RollbackNetworkTask, 5, "Network call with 5 retries")
                
                // Database updates are more reliable - fewer retries needed
                .AddStep(ProcessingState.NetworkCall, DatabaseUpdateTask, RollbackDatabaseTask, 2, "Database update with 2 retries")
                
                // File operations are very reliable - minimal retries
                .AddStep(ProcessingState.DatabaseUpdate, FileOperationTask, RollbackFileTask, 1, "File operation with 1 retry")
                
                // Final step uses process default
                .AddStep(ProcessingState.FileOperation, CompletionTask, RollbackCompletionTask, "Completion step uses process default")
                
                .WithSequence(
                    ProcessingState.Initial,
                    ProcessingState.NetworkCall,
                    ProcessingState.DatabaseUpdate,
                    ProcessingState.FileOperation,
                    ProcessingState.Completed)
                .WithMaxRetries(3) // Process default for steps without specific retries
                .Build(task);

            Console.WriteLine("Process configuration:");
            Console.WriteLine($"- Process default max retries: {process.Configuration.MaxRetries}");
            
            var initialStep = process.GetStepDefinition(ProcessingState.Initial);
            Console.WriteLine($"- Network call step max retries: {initialStep.GetEffectiveMaxRetries(process.Configuration.MaxRetries)}");
            
            var networkStep = process.GetStepDefinition(ProcessingState.NetworkCall);
            Console.WriteLine($"- Database update step max retries: {networkStep.GetEffectiveMaxRetries(process.Configuration.MaxRetries)}");
            
            var dbStep = process.GetStepDefinition(ProcessingState.DatabaseUpdate);
            Console.WriteLine($"- File operation step max retries: {dbStep.GetEffectiveMaxRetries(process.Configuration.MaxRetries)}");
            
            var fileStep = process.GetStepDefinition(ProcessingState.FileOperation);
            Console.WriteLine($"- Completion step max retries: {fileStep.GetEffectiveMaxRetries(process.Configuration.MaxRetries)} (uses process default)");

            // Execute the process
            Console.WriteLine("\nExecuting process...");
            var (success, lastResult) = await process.ExecuteToCompletion();

            // Display results
            Console.WriteLine($"\nProcess finished. Success: {success}");
            Console.WriteLine($"Final state: {task.Status}");
            Console.WriteLine($"Network call attempts: {task.NetworkCallAttempts}");
            Console.WriteLine($"Database update attempts: {task.DatabaseUpdateAttempts}");
            Console.WriteLine($"File operation attempts: {task.FileOperationAttempts}");
            
            Console.WriteLine("\nExecution log:");
            foreach (var logEntry in task.Log)
            {
                Console.WriteLine($"  - {logEntry}");
            }

            return success;
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> NetworkCallTask(ProcessingTask task)
        {
            await Task.Delay(100); // Simulate network delay
            task.NetworkCallAttempts++;
            task.Log.Add($"Network call attempt #{task.NetworkCallAttempts}");

            // Simulate network failures for first few attempts
            if (task.NetworkCallAttempts <= 3)
            {
                task.Log.Add($"Network call failed (attempt {task.NetworkCallAttempts})");
                return (MultiStepProcessTaskRunResult.FailedRetry, "Network timeout");
            }

            task.Log.Add("Network call succeeded");
            return (MultiStepProcessTaskRunResult.Successful, "Network data received");
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> RollbackNetworkTask(ProcessingTask task)
        {
            await Task.Delay(50);
            task.Log.Add("Network call rollback completed");
            return (MultiStepProcessTaskRunResult.Successful, null);
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> DatabaseUpdateTask(ProcessingTask task)
        {
            await Task.Delay(50);
            task.DatabaseUpdateAttempts++;
            task.Log.Add($"Database update attempt #{task.DatabaseUpdateAttempts}");

            // Simulate database failures for first attempt only
            if (task.DatabaseUpdateAttempts == 1)
            {
                task.Log.Add("Database update failed (connection timeout)");
                return (MultiStepProcessTaskRunResult.FailedRetry, "Database timeout");
            }

            task.Log.Add("Database update succeeded");
            return (MultiStepProcessTaskRunResult.Successful, "Database updated");
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> RollbackDatabaseTask(ProcessingTask task)
        {
            await Task.Delay(50);
            task.Log.Add("Database rollback completed");
            return (MultiStepProcessTaskRunResult.Successful, null);
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> FileOperationTask(ProcessingTask task)
        {
            await Task.Delay(30);
            task.FileOperationAttempts++;
            task.Log.Add($"File operation attempt #{task.FileOperationAttempts}");
            task.Log.Add("File operation succeeded");
            return (MultiStepProcessTaskRunResult.Successful, "File written");
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> RollbackFileTask(ProcessingTask task)
        {
            await Task.Delay(30);
            task.Log.Add("File operation rollback completed");
            return (MultiStepProcessTaskRunResult.Successful, null);
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> CompletionTask(ProcessingTask task)
        {
            await Task.Delay(20);
            task.Log.Add("Process completed successfully");
            return (MultiStepProcessTaskRunResult.Successful, "Process complete");
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> RollbackCompletionTask(ProcessingTask task)
        {
            await Task.Delay(20);
            task.Log.Add("Completion rollback completed");
            return (MultiStepProcessTaskRunResult.Successful, null);
        }
    }
}
