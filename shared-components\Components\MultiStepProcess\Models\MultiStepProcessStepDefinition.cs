using shared.Components.MultiStepProcess.Enums;
using shared.Models.Interfaces;

namespace shared.Components.MultiStepProcess.Models
{
    /// <summary>
    /// Defines a single step in a multi-step process.
    /// Contains both the forward (StateTask) and backward (RollbackTask) operations for a state.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public class MultiStepProcessStepDefinition<TState, TObject>
        where TObject : IStateful<TState>
        where TState : notnull
    {
        /// <summary>
        /// The state this step definition applies to.
        /// </summary>
        public TState State { get; set; }

        /// <summary>
        /// The task to execute when the process is in Running state.
        /// This task moves the process forward.
        /// </summary>
        public Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> StateTask { get; set; }

        /// <summary>
        /// The task to execute when the process is in RollingBack state.
        /// This task undoes the work done by the StateTask.
        /// </summary>
        public Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> RollbackTask { get; set; }

        /// <summary>
        /// Optional timeout for the StateTask. If null, uses process default or no timeout.
        /// </summary>
        public TimeSpan? StateTaskTimeout { get; set; }

        /// <summary>
        /// Optional timeout for the RollbackTask. If null, uses process default or no timeout.
        /// </summary>
        public TimeSpan? RollbackTaskTimeout { get; set; }

        /// <summary>
        /// Optional description of what this step does.
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Maximum number of retries allowed for this specific step.
        /// If null, uses the process-level MaxRetries configuration.
        /// </summary>
        public int? MaxRetries { get; set; }

        /// <summary>
        /// The next state to transition to when StateTask succeeds.
        /// Null indicates this is the final state.
        /// </summary>
        public TState? NextState { get; set; }

        /// <summary>
        /// The previous state to transition to when RollbackTask succeeds.
        /// Null indicates this is the first state (rollback complete).
        /// </summary>
        public TState? PreviousState { get; set; }

        /// <summary>
        /// Whether this is the final step in the process.
        /// </summary>
        public bool IsFinalStep => NextState == null;

        /// <summary>
        /// Whether this is the first step in the process.
        /// </summary>
        public bool IsFirstStep => PreviousState == null;

        /// <summary>
        /// Creates a new step definition.
        /// </summary>
        /// <param name="state">The state this step applies to</param>
        /// <param name="stateTask">Task to execute when moving forward</param>
        /// <param name="rollbackTask">Task to execute when rolling back</param>
        /// <param name="description">Optional description</param>
        public MultiStepProcessStepDefinition(
            TState state,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> stateTask,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> rollbackTask,
            string? description = null)
        {
            State = state ?? throw new ArgumentNullException(nameof(state));
            StateTask = stateTask ?? throw new ArgumentNullException(nameof(stateTask));
            RollbackTask = rollbackTask ?? throw new ArgumentNullException(nameof(rollbackTask));
            Description = description;
        }

        /// <summary>
        /// Parameterless constructor for serialization.
        /// </summary>
        public MultiStepProcessStepDefinition()
        {
            State = default(TState)!;
            StateTask = null!;
            RollbackTask = null!;
        }

        /// <summary>
        /// Sets the timeout for the StateTask.
        /// </summary>
        /// <param name="timeout">Timeout duration</param>
        /// <returns>This step definition for method chaining</returns>
        public MultiStepProcessStepDefinition<TState, TObject> WithStateTaskTimeout(TimeSpan timeout)
        {
            StateTaskTimeout = timeout;
            return this;
        }

        /// <summary>
        /// Sets the timeout for the RollbackTask.
        /// </summary>
        /// <param name="timeout">Timeout duration</param>
        /// <returns>This step definition for method chaining</returns>
        public MultiStepProcessStepDefinition<TState, TObject> WithRollbackTaskTimeout(TimeSpan timeout)
        {
            RollbackTaskTimeout = timeout;
            return this;
        }

        /// <summary>
        /// Sets the maximum number of retries for this step.
        /// </summary>
        /// <param name="maxRetries">Maximum number of retries for this step</param>
        /// <returns>This step definition for method chaining</returns>
        public MultiStepProcessStepDefinition<TState, TObject> WithMaxRetries(int maxRetries)
        {
            if (maxRetries < 0)
                throw new ArgumentException("MaxRetries must be non-negative", nameof(maxRetries));

            MaxRetries = maxRetries;
            return this;
        }

        /// <summary>
        /// Gets the effective maximum retries for this step.
        /// Returns step-level MaxRetries if set, otherwise falls back to process-level maxRetries.
        /// </summary>
        /// <param name="processMaxRetries">Process-level maximum retries to use as fallback</param>
        /// <returns>The effective maximum retries for this step</returns>
        public int GetEffectiveMaxRetries(int processMaxRetries)
        {
            return MaxRetries ?? processMaxRetries;
        }

        /// <summary>
        /// Sets the next state for successful StateTask execution.
        /// </summary>
        /// <param name="nextState">The next state</param>
        /// <returns>This step definition for method chaining</returns>
        public MultiStepProcessStepDefinition<TState, TObject> WithNextState(TState? nextState)
        {
            NextState = nextState;
            return this;
        }

        /// <summary>
        /// Sets the previous state for successful RollbackTask execution.
        /// </summary>
        /// <param name="previousState">The previous state</param>
        /// <returns>This step definition for method chaining</returns>
        public MultiStepProcessStepDefinition<TState, TObject> WithPreviousState(TState? previousState)
        {
            PreviousState = previousState;
            return this;
        }

        /// <summary>
        /// Gets the appropriate task based on the process state.
        /// </summary>
        /// <param name="processState">Current process state</param>
        /// <returns>The task to execute</returns>
        public Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> GetTaskForProcessState(MultiStepProcessState processState)
        {
            return processState == MultiStepProcessState.Running ? StateTask : RollbackTask;
        }

        /// <summary>
        /// Gets the appropriate timeout based on the process state.
        /// </summary>
        /// <param name="processState">Current process state</param>
        /// <returns>The timeout for the task, or null if no timeout</returns>
        public TimeSpan? GetTimeoutForProcessState(MultiStepProcessState processState)
        {
            return processState == MultiStepProcessState.Running ? StateTaskTimeout : RollbackTaskTimeout;
        }

        /// <summary>
        /// Gets the next state to transition to based on the process state.
        /// </summary>
        /// <param name="processState">Current process state</param>
        /// <returns>The next state to transition to, or null if this is a terminal state</returns>
        public TState? GetNextStateForProcessState(MultiStepProcessState processState)
        {
            return processState == MultiStepProcessState.Running ? NextState : PreviousState;
        }
    }
}
