# MultiStepProcess Component

The MultiStepProcess component provides a double-linked list builder and runner for executing multiple steps in the correct order, with the ability to revert execution if the process needs to be aborted. It supports both forward execution (StateTask) and rollback execution (RollbackTask) for each step.

## Key Features

- **Double-linked list structure**: Steps are linked both forward and backward for proper execution and rollback
- **State management**: Tracks process state (Running/RollingBack) and individual step states
- **Retry logic**: Configurable retry behavior with automatic rollback on failure
- **Timeout support**: Optional timeouts for both StateTask and RollbackTask
- **Serializable state**: Complete state data can be saved/loaded for persistence
- **Comprehensive error handling**: Detailed failure reasons and exception tracking
- **Fluent configuration**: Builder pattern for easy process setup

## Core Concepts

### Process States

- **Running**: Process executes StateTask for each step, moving forward through the sequence
- **RollingBack**: Process executes RollbackTask for each step, moving backward through the sequence

### Task Results

- **Successful**: Task completed successfully, proceed to next state
- **FailedRetry**: Task failed but should be retried (increments retry counter)
- **FailedAbort**: Task failed and process should abort (triggers rollback if in Running state)
- **FailedTimeout**: Task timed out (behavior configurable as retry or abort)

### Failure Reasons

- **None**: No failure occurred
- **Timeout**: Task execution timed out
- **RetryLimitExceeded**: Maximum retry limit was exceeded
- **TaskAborted**: Task explicitly requested abort
- **RollbackFailed**: Rollback process failed and cannot continue
- **InvalidTransition**: Invalid state transition attempted
- **InvalidConfiguration**: Process configuration is invalid

## Quick Start

### 1. Define Your State Enum and Business Object Class

```csharp
public enum OrderProcessingState
{
    Created,
    PaymentProcessing,
    InventoryReserved,
    Shipped,
    Completed
}

public class Order : IStateful<OrderProcessingState>
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public decimal Amount { get; set; }
    public string CustomerId { get; set; } = string.Empty;
    public OrderProcessingState Status { get; set; } = OrderProcessingState.Created;
    public List<string> ProcessingLog { get; set; } = new List<string>();
}
```

### 2. Create and Configure the Process

**Option A: Direct Configuration**
```csharp
var order = new Order { Amount = 100.00m, CustomerId = "CUST123" };

var process = new MultiStepProcess<OrderProcessingState, Order>(order)
    .ConfigureStep(OrderProcessingState.Created, ProcessPaymentTask, RollbackPaymentTask, "Process payment")
    .ConfigureStep(OrderProcessingState.PaymentProcessing, ReserveInventoryTask, ReleaseInventoryTask, "Reserve inventory")
    .ConfigureStep(OrderProcessingState.InventoryReserved, ShipOrderTask, CancelShipmentTask, "Ship order")
    .ConfigureStep(OrderProcessingState.Shipped, CompleteOrderTask, RevertCompletionTask, "Complete order")
    .DefineSequence(
        OrderProcessingState.Created,
        OrderProcessingState.PaymentProcessing,
        OrderProcessingState.InventoryReserved,
        OrderProcessingState.Shipped,
        OrderProcessingState.Completed);
```

**Option B: Builder Pattern**
```csharp
var process = new MultiStepProcessBuilder<OrderProcessingState, Order>()
    .AddStep(OrderProcessingState.Created, ProcessPaymentTask, RollbackPaymentTask, "Process payment")
    .AddStep(OrderProcessingState.PaymentProcessing, ReserveInventoryTask, ReleaseInventoryTask, "Reserve inventory")
    .AddStep(OrderProcessingState.InventoryReserved, ShipOrderTask, CancelShipmentTask, "Ship order")
    .AddStep(OrderProcessingState.Shipped, CompleteOrderTask, RevertCompletionTask, "Complete order")
    .WithSequence(
        OrderProcessingState.Created,
        OrderProcessingState.PaymentProcessing,
        OrderProcessingState.InventoryReserved,
        OrderProcessingState.Shipped,
        OrderProcessingState.Completed)
    .WithMaxRetries(3)
    .WithTimeoutBehavior(MultiStepProcessTaskRunResult.FailedRetry)
    .Build(order);
```

### 3. Execute the Process

**Iteration by Iteration**
```csharp
while (!process.IsFinished)
{
    var (success, result) = await process.ExecuteIteration();
    
    if (!success)
    {
        Console.WriteLine($"Process failed: {process.FailureReason}");
        break;
    }
    
    Console.WriteLine($"Current state: {process.CurrentState}, Process state: {process.ProcessState}");
}
```

**Execute to Completion**
```csharp
var (success, lastResult) = await process.ExecuteToCompletion();

if (success)
{
    Console.WriteLine("Process completed successfully");
}
else
{
    Console.WriteLine($"Process failed: {process.FailureReason}");
    if (process.LastException != null)
    {
        Console.WriteLine($"Last exception: {process.LastException.Message}");
    }
}
```

## Advanced Features

### Timeouts

Configure timeouts for individual tasks:

```csharp
process.ConfigureStep(
    OrderProcessingState.PaymentProcessing,
    ProcessPaymentTask,
    RollbackPaymentTask,
    TimeSpan.FromSeconds(30), // StateTask timeout
    TimeSpan.FromSeconds(10), // RollbackTask timeout
    "Process payment with timeouts");
```

### State Persistence

Save and load process state:

```csharp
// Save state
var stateData = process.StateData;
var json = JsonSerializer.Serialize(stateData);

// Load state
var loadedStateData = JsonSerializer.Deserialize<MultiStepProcessStateData<OrderProcessingState, Order>>(json);
var restoredProcess = new MultiStepProcess<OrderProcessingState, Order>();
// ... configure steps and sequence ...
restoredProcess.LoadStateData(loadedStateData);
```

### Manual Rollback

Force the process to start rolling back:

```csharp
if (someCondition)
{
    process.StartRollback();
}
```

### Process Information

Get detailed process information for debugging:

```csharp
var info = process.GetProcessInfo();
foreach (var kvp in info)
{
    Console.WriteLine($"{kvp.Key}: {kvp.Value}");
}
```

## Task Implementation Examples

```csharp
private static async Task<(MultiStepProcessTaskRunResult, object?)> ProcessPaymentTask(Order order)
{
    try
    {
        // Simulate payment processing
        await Task.Delay(1000);
        
        // Simulate random failure for demonstration
        if (new Random().NextDouble() < 0.1)
        {
            return (MultiStepProcessTaskRunResult.FailedRetry, "Payment gateway temporarily unavailable");
        }
        
        order.ProcessingLog.Add($"Payment processed: ${order.Amount}");
        return (MultiStepProcessTaskRunResult.Successful, $"Payment of ${order.Amount} processed successfully");
    }
    catch (Exception ex)
    {
        return (MultiStepProcessTaskRunResult.FailedAbort, $"Payment processing failed: {ex.Message}");
    }
}

private static async Task<(MultiStepProcessTaskRunResult, object?)> RollbackPaymentTask(Order order)
{
    try
    {
        // Simulate payment refund
        await Task.Delay(500);
        
        order.ProcessingLog.Add($"Payment refunded: ${order.Amount}");
        return (MultiStepProcessTaskRunResult.Successful, $"Payment of ${order.Amount} refunded successfully");
    }
    catch (Exception ex)
    {
        return (MultiStepProcessTaskRunResult.FailedAbort, $"Payment refund failed: {ex.Message}");
    }
}
```

## Configuration Options

### MultiStepProcessConfiguration

- **MaxRetries**: Maximum number of retries per step (default: 3) - serves as fallback when step-level retries are not specified
- **TimeoutBehavior**: How to handle timeouts - FailedRetry or FailedAbort (default: FailedRetry)
- **DefaultTaskTimeout**: Default timeout for tasks that don't specify their own (default: null)
- **ResetRetryCountOnStateTransition**: Whether to reset retry count on successful transitions (default: true)
- **ValidateConfigurationOnInit**: Whether to validate configuration on initialization (default: true)

### Step-Level Max Retries

Each step can have its own maximum retry count that overrides the process-level default:

**Direct Configuration:**
```csharp
process.ConfigureStep(MyState.Processing, ProcessTask, RollbackTask, maxRetries: 5, "Step with 5 retries");
```

**Builder Pattern:**
```csharp
var process = new MultiStepProcessBuilder<MyState, MyObject>()
    .AddStep(MyState.Initial, InitTask, RollbackTask, 2, "Initial step with 2 retries")
    .AddStep(MyState.Processing, ProcessTask, RollbackTask, 10, "Processing step with 10 retries")
    .AddStep(MyState.Final, FinalTask, RollbackTask) // Uses process default
    .WithMaxRetries(3) // Process default for steps without specific retries
    .Build(myObject);
```

**With Timeouts and Retries:**
```csharp
var process = new MultiStepProcessBuilder<MyState, MyObject>()
    .AddStep(MyState.Processing, ProcessTask, RollbackTask,
        TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(2), 7, "Complex step")
    .Build(myObject);
```

**Retry Priority:**
1. Step-level MaxRetries (if specified)
2. Process-level MaxRetries (fallback)

## Error Handling

The component provides comprehensive error handling with specific exception types:

- **MultiStepProcessException**: Base exception for all process-related errors
- **StepConfigurationException**: Invalid or missing step configuration
- **StepTaskExecutionException**: Task execution failure
- **MaxRetryExceededException**: Retry limit exceeded
- **ProcessConfigurationException**: Invalid process configuration

## Best Practices

1. **Always implement both StateTask and RollbackTask** for each step
2. **Use meaningful descriptions** for steps to aid in debugging
3. **Handle exceptions gracefully** in your task implementations
4. **Set appropriate timeouts** for long-running operations
5. **Use correlation IDs** for tracking across distributed systems
6. **Validate your process configuration** before execution
7. **Save state data** for long-running processes that might need to be resumed
8. **Configure step-level retries** based on the reliability characteristics of each step:
   - Use higher retry counts for steps that interact with external services
   - Use lower retry counts for steps that are likely to fail consistently
   - Consider the cost and time impact of retries for each step
9. **Monitor retry counts** and adjust MaxRetries based on your use case

## Thread Safety

The MultiStepProcess component is thread-safe for concurrent read operations, but write operations (like ExecuteIteration) should be serialized. Use appropriate locking mechanisms if you need to execute the same process instance from multiple threads.
