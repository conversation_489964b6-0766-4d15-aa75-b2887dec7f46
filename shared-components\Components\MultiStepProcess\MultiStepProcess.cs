using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Exceptions;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Interfaces;
using System.Collections.Concurrent;

namespace shared.Components.MultiStepProcess
{
    /// <summary>
    /// Implementation of a multi-step process that can execute forward and rollback operations.
    /// Provides a double-linked list builder and runner for executing multiple steps in order
    /// with the ability to revert execution if the process needs to be aborted.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public class MultiStepProcess<TState, TObject> : IMultiStepProcess<TState, TObject>
        where TObject : IStateful<TState>
        where TState : notnull
    {
        private readonly ConcurrentDictionary<TState, MultiStepProcessStepDefinition<TState, TObject>> _stepDefinitions;
        private readonly object _lockObject = new object();

        private MultiStepProcessStateData<TState, TObject>? _stateData;
        private bool _lastIterationFailed;
        private Exception? _lastException;

        /// <summary>
        /// Gets the current state data of the multi-step process.
        /// </summary>
        public MultiStepProcessStateData<TState, TObject> StateData => 
            _stateData ?? throw new InvalidOperationException("Process has not been initialized. Call Initialize() or LoadStateData() first.");

        /// <summary>
        /// Gets the current state of the IStateful object.
        /// </summary>
        public TState CurrentState => StateData.CurrentState;

        /// <summary>
        /// Gets the current process state (Running or RollingBack).
        /// </summary>
        public MultiStepProcessState ProcessState => StateData.ProcessState;

        /// <summary>
        /// Gets whether the process has finished processing.
        /// </summary>
        public bool IsFinished => StateData.IsFinished;

        /// <summary>
        /// Gets the current retry count for the current step.
        /// </summary>
        public int RetryCount => StateData.RetryCount;

        /// <summary>
        /// Gets the reason for process failure, if any.
        /// </summary>
        public MultiStepProcessFailureReason FailureReason => StateData.FailureReason;

        /// <summary>
        /// Gets the process configuration.
        /// </summary>
        public MultiStepProcessConfiguration Configuration { get; private set; }

        /// <summary>
        /// Gets whether the last iteration failed.
        /// </summary>
        public bool LastIterationFailed => _lastIterationFailed;

        /// <summary>
        /// Gets the last exception that occurred, if any.
        /// </summary>
        public Exception? LastException => _lastException;

        /// <summary>
        /// Creates a new MultiStepProcess with default configuration.
        /// </summary>
        public MultiStepProcess() : this(new MultiStepProcessConfiguration())
        {
        }

        /// <summary>
        /// Creates a new MultiStepProcess with the specified configuration.
        /// </summary>
        /// <param name="configuration">Process configuration</param>
        public MultiStepProcess(MultiStepProcessConfiguration configuration)
        {
            Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _stepDefinitions = new ConcurrentDictionary<TState, MultiStepProcessStepDefinition<TState, TObject>>();

            if (Configuration.ValidateConfigurationOnInit)
            {
                Configuration.Validate();
            }
        }

        /// <summary>
        /// Creates a new MultiStepProcess with an initial object and default configuration.
        /// </summary>
        /// <param name="statefulObject">The initial IStateful object</param>
        /// <param name="correlationId">Optional correlation ID</param>
        public MultiStepProcess(TObject statefulObject, string? correlationId = null) 
            : this(new MultiStepProcessConfiguration())
        {
            Initialize(statefulObject, correlationId);
        }

        /// <summary>
        /// Creates a new MultiStepProcess with an initial object and specified configuration.
        /// </summary>
        /// <param name="statefulObject">The initial IStateful object</param>
        /// <param name="configuration">Process configuration</param>
        /// <param name="correlationId">Optional correlation ID</param>
        public MultiStepProcess(TObject statefulObject, MultiStepProcessConfiguration configuration, string? correlationId = null) 
            : this(configuration)
        {
            Initialize(statefulObject, correlationId);
        }

        /// <summary>
        /// Configures a step in the process with both StateTask and RollbackTask.
        /// </summary>
        public IMultiStepProcess<TState, TObject> ConfigureStep(
            TState state,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> stateTask,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> rollbackTask,
            string? description = null)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (stateTask == null) throw new ArgumentNullException(nameof(stateTask));
            if (rollbackTask == null) throw new ArgumentNullException(nameof(rollbackTask));

            var stepDefinition = new MultiStepProcessStepDefinition<TState, TObject>(state, stateTask, rollbackTask, description);
            _stepDefinitions.AddOrUpdate(state, stepDefinition, (key, existing) => stepDefinition);

            return this;
        }

        /// <summary>
        /// Configures a step with timeouts for both StateTask and RollbackTask.
        /// </summary>
        public IMultiStepProcess<TState, TObject> ConfigureStep(
            TState state,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> stateTask,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> rollbackTask,
            TimeSpan stateTaskTimeout,
            TimeSpan rollbackTaskTimeout,
            string? description = null)
        {
            var stepDefinition = new MultiStepProcessStepDefinition<TState, TObject>(state, stateTask, rollbackTask, description)
                .WithStateTaskTimeout(stateTaskTimeout)
                .WithRollbackTaskTimeout(rollbackTaskTimeout);

            _stepDefinitions.AddOrUpdate(state, stepDefinition, (key, existing) => stepDefinition);

            return this;
        }

        /// <summary>
        /// Defines the sequence of states by linking them together.
        /// </summary>
        public IMultiStepProcess<TState, TObject> DefineSequence(params TState[] stateSequence)
        {
            if (stateSequence == null) throw new ArgumentNullException(nameof(stateSequence));
            if (stateSequence.Length == 0) throw new ArgumentException("State sequence cannot be empty", nameof(stateSequence));

            // Link states together in a double-linked list structure
            for (int i = 0; i < stateSequence.Length; i++)
            {
                var currentState = stateSequence[i];
                
                if (!_stepDefinitions.TryGetValue(currentState, out var stepDefinition))
                {
                    throw new InvalidOperationException($"Step definition for state '{currentState}' not found. Configure the step first using ConfigureStep().");
                }

                // Set next state (null for last state)
                stepDefinition.NextState = i < stateSequence.Length - 1 ? stateSequence[i + 1] : default(TState);
                
                // Set previous state (null for first state)
                stepDefinition.PreviousState = i > 0 ? stateSequence[i - 1] : default(TState);
            }

            return this;
        }

        /// <summary>
        /// Initializes the process with a TObject instance.
        /// </summary>
        public void Initialize(TObject statefulObject, string? correlationId = null)
        {
            if (statefulObject == null) throw new ArgumentNullException(nameof(statefulObject));
            if (_stateData != null) throw new InvalidOperationException("Process has already been initialized. Use LoadStateData() to replace existing state.");

            _stateData = new MultiStepProcessStateData<TState, TObject>(statefulObject, correlationId);
            _lastIterationFailed = false;
            _lastException = null;
        }

        /// <summary>
        /// Loads the complete state data into the process.
        /// </summary>
        public void LoadStateData(MultiStepProcessStateData<TState, TObject> stateData)
        {
            _stateData = stateData ?? throw new ArgumentNullException(nameof(stateData));
            _lastIterationFailed = false;
            _lastException = null;
        }

        /// <summary>
        /// Sets the current state of the process and the IStateful object.
        /// </summary>
        public void SetCurrentState(TState state, MultiStepProcessState processState = MultiStepProcessState.Running)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (_stateData == null) throw new InvalidOperationException("Process has not been initialized. Call Initialize() or LoadStateData() first.");

            lock (_lockObject)
            {
                _stateData.StatefulObject.Status = state;
                _stateData.UpdateProcessState(processState);
                _stateData.ResetRetryCount();
                _lastIterationFailed = false;
                _lastException = null;
            }
        }

        /// <summary>
        /// Forces the process to start rolling back from the current state.
        /// </summary>
        public void StartRollback()
        {
            if (_stateData == null) throw new InvalidOperationException("Process has not been initialized. Call Initialize() or LoadStateData() first.");

            lock (_lockObject)
            {
                _stateData.UpdateProcessState(MultiStepProcessState.RollingBack);
                _stateData.ResetRetryCount();
            }
        }

        /// <summary>
        /// Validates that the process configuration is valid and complete.
        /// </summary>
        public bool ValidateProcess()
        {
            if (_stepDefinitions.IsEmpty)
                throw new ProcessConfigurationException("No steps have been configured. Use ConfigureStep() to add steps.");

            // Validate configuration
            try
            {
                Configuration.Validate();
            }
            catch (ArgumentException ex)
            {
                throw new ProcessConfigurationException($"Invalid process configuration: {ex.Message}", ex);
            }

            // Validate that all configured steps have valid transitions
            foreach (var kvp in _stepDefinitions)
            {
                var state = kvp.Key;
                var stepDef = kvp.Value;

                // Validate step definition
                ValidateStepDefinition(state, stepDef);

                // Check that next state exists if not final
                if (stepDef.NextState != null && !_stepDefinitions.ContainsKey(stepDef.NextState))
                {
                    throw new ProcessConfigurationException($"Step '{state}' references next state '{stepDef.NextState}' which is not configured.");
                }

                // Check that previous state exists if not first
                if (stepDef.PreviousState != null && !_stepDefinitions.ContainsKey(stepDef.PreviousState))
                {
                    throw new ProcessConfigurationException($"Step '{state}' references previous state '{stepDef.PreviousState}' which is not configured.");
                }
            }

            // Validate sequence integrity
            ValidateSequenceIntegrity();

            return true;
        }

        /// <summary>
        /// Validates a single step definition.
        /// </summary>
        private void ValidateStepDefinition(TState state, MultiStepProcessStepDefinition<TState, TObject> stepDef)
        {
            if (stepDef.StateTask == null)
                throw new StepConfigurationException(state, $"StateTask is null for step '{state}'");

            if (stepDef.RollbackTask == null)
                throw new StepConfigurationException(state, $"RollbackTask is null for step '{state}'");

            if (stepDef.StateTaskTimeout.HasValue && stepDef.StateTaskTimeout.Value <= TimeSpan.Zero)
                throw new StepConfigurationException(state, $"StateTaskTimeout must be positive for step '{state}'");

            if (stepDef.RollbackTaskTimeout.HasValue && stepDef.RollbackTaskTimeout.Value <= TimeSpan.Zero)
                throw new StepConfigurationException(state, $"RollbackTaskTimeout must be positive for step '{state}'");
        }

        /// <summary>
        /// Validates the sequence integrity to ensure proper linking.
        /// </summary>
        private void ValidateSequenceIntegrity()
        {
            // Find steps with no previous state (should be exactly one - the first step)
            var firstSteps = _stepDefinitions.Values.Where(s => s.PreviousState == null).ToList();
            if (firstSteps.Count == 0)
                throw new ProcessConfigurationException("No first step found. At least one step must have PreviousState = null.");
            if (firstSteps.Count > 1)
                throw new ProcessConfigurationException($"Multiple first steps found ({firstSteps.Count}). Only one step should have PreviousState = null.");

            // Find steps with no next state (should be exactly one - the last step)
            var lastSteps = _stepDefinitions.Values.Where(s => s.NextState == null).ToList();
            if (lastSteps.Count == 0)
                throw new ProcessConfigurationException("No last step found. At least one step must have NextState = null.");
            if (lastSteps.Count > 1)
                throw new ProcessConfigurationException($"Multiple last steps found ({lastSteps.Count}). Only one step should have NextState = null.");

            // Validate that the sequence forms a proper chain
            ValidateSequenceChain(firstSteps[0]);
        }

        /// <summary>
        /// Validates that the sequence forms a proper forward and backward chain.
        /// </summary>
        private void ValidateSequenceChain(MultiStepProcessStepDefinition<TState, TObject> firstStep)
        {
            var visitedStates = new HashSet<TState>();
            var currentStep = firstStep;

            // Walk forward through the chain
            while (currentStep != null)
            {
                if (visitedStates.Contains(currentStep.State))
                    throw new ProcessConfigurationException($"Circular reference detected in sequence at state '{currentStep.State}'");

                visitedStates.Add(currentStep.State);

                if (currentStep.NextState == null)
                    break; // Reached the end

                if (!_stepDefinitions.TryGetValue(currentStep.NextState, out var nextStep))
                    throw new ProcessConfigurationException($"Step '{currentStep.State}' references next state '{currentStep.NextState}' which is not configured.");

                // Validate backward link
                if (!EqualityComparer<TState>.Default.Equals(nextStep.PreviousState, currentStep.State))
                    throw new ProcessConfigurationException($"Broken backward link: Step '{nextStep.State}' should have PreviousState = '{currentStep.State}' but has '{nextStep.PreviousState}'");

                currentStep = nextStep;
            }

            // Ensure all configured steps are reachable
            if (visitedStates.Count != _stepDefinitions.Count)
            {
                var unreachableStates = _stepDefinitions.Keys.Except(visitedStates);
                throw new ProcessConfigurationException($"Unreachable steps found: {string.Join(", ", unreachableStates)}");
            }
        }

        /// <summary>
        /// Checks if the process can execute another iteration.
        /// </summary>
        public bool CanExecute()
        {
            return _stateData != null && !_stateData.IsFinished;
        }

        /// <summary>
        /// Executes one complete iteration of the process.
        /// </summary>
        public async Task<(bool success, object? result)> ExecuteIteration()
        {
            if (_stateData == null) throw new InvalidOperationException("Process has not been initialized. Call Initialize() or LoadStateData() first.");
            if (_stateData.IsFinished) throw new InvalidOperationException("Process has already finished and cannot execute further iterations.");

            try
            {
                _lastIterationFailed = false;
                _lastException = null;

                // Get the current step definition
                if (!_stepDefinitions.TryGetValue(_stateData.CurrentState, out var stepDefinition))
                {
                    var exception = new StepConfigurationException(_stateData.CurrentState, $"No step definition found for state '{_stateData.CurrentState}'");
                    _lastException = exception;
                    _lastIterationFailed = true;
                    _stateData.MarkAsFinished(MultiStepProcessFailureReason.InvalidConfiguration);
                    return (false, null);
                }

                // Execute the appropriate task based on process state
                var (taskResult, resultObject) = await ExecuteStepTask(stepDefinition);

                // Handle the task result
                return await HandleTaskResult(taskResult, resultObject, stepDefinition);
            }
            catch (Exception ex)
            {
                _lastException = ex;
                _lastIterationFailed = true;
                _stateData.MarkAsFinished(MultiStepProcessFailureReason.TaskAborted);
                return (false, null);
            }
        }

        /// <summary>
        /// Executes the process until it reaches a finished state or fails.
        /// </summary>
        public async Task<(bool success, object? lastResult)> ExecuteToCompletion(int maxIterations = 100)
        {
            int iterations = 0;
            object? lastResult = null;

            while (!IsFinished && iterations < maxIterations)
            {
                var (success, result) = await ExecuteIteration();
                lastResult = result;

                if (!success)
                {
                    return (false, lastResult);
                }

                iterations++;
            }

            return (IsFinished, lastResult);
        }

        /// <summary>
        /// Executes the appropriate task for the current step and process state.
        /// </summary>
        private async Task<(MultiStepProcessTaskRunResult, object?)> ExecuteStepTask(MultiStepProcessStepDefinition<TState, TObject> stepDefinition)
        {
            var task = stepDefinition.GetTaskForProcessState(_stateData.ProcessState);
            var timeout = stepDefinition.GetTimeoutForProcessState(_stateData.ProcessState) ?? Configuration.DefaultTaskTimeout;

            try
            {
                if (timeout.HasValue)
                {
                    // Execute with timeout
                    using var cts = new CancellationTokenSource(timeout.Value);
                    var taskWithCancellation = Task.Run(async () => await task(_stateData.StatefulObject), cts.Token);
                    return await taskWithCancellation;
                }
                else
                {
                    // Execute without timeout
                    return await task(_stateData.StatefulObject);
                }
            }
            catch (OperationCanceledException)
            {
                // Task timed out
                return (MultiStepProcessTaskRunResult.FailedTimeout, null);
            }
            catch (Exception ex)
            {
                // Wrap and rethrow as step execution exception
                throw new StepTaskExecutionException(
                    _stateData.CurrentState,
                    _stateData.ProcessState,
                    $"Task execution failed for state '{_stateData.CurrentState}' in process state '{_stateData.ProcessState}': {ex.Message}",
                    ex);
            }
        }

        /// <summary>
        /// Handles the result of a task execution and determines next actions.
        /// </summary>
        private async Task<(bool success, object? result)> HandleTaskResult(
            MultiStepProcessTaskRunResult taskResult,
            object? resultObject,
            MultiStepProcessStepDefinition<TState, TObject> stepDefinition)
        {
            switch (taskResult)
            {
                case MultiStepProcessTaskRunResult.Successful:
                    return HandleSuccessfulTask(resultObject, stepDefinition);

                case MultiStepProcessTaskRunResult.FailedRetry:
                    return HandleRetryableFailure(resultObject);

                case MultiStepProcessTaskRunResult.FailedAbort:
                    return HandleAbortFailure(resultObject);

                case MultiStepProcessTaskRunResult.FailedTimeout:
                    return HandleTimeoutFailure(resultObject);

                default:
                    throw new ArgumentOutOfRangeException(nameof(taskResult), taskResult, "Unknown task result");
            }
        }

        /// <summary>
        /// Handles successful task execution.
        /// </summary>
        private (bool success, object? result) HandleSuccessfulTask(object? resultObject, MultiStepProcessStepDefinition<TState, TObject> stepDefinition)
        {
            lock (_lockObject)
            {
                // Reset retry count on successful transition if configured
                if (Configuration.ResetRetryCountOnStateTransition)
                {
                    _stateData.ResetRetryCount();
                }

                // Get the next state based on process state
                var nextState = stepDefinition.GetNextStateForProcessState(_stateData.ProcessState);

                if (nextState == null)
                {
                    // This is a terminal state - process is complete
                    _stateData.MarkAsFinished(MultiStepProcessFailureReason.None);
                    return (true, resultObject);
                }

                // Transition to next state
                _stateData.StatefulObject.Status = nextState;
                return (true, resultObject);
            }
        }

        /// <summary>
        /// Handles retryable task failure.
        /// </summary>
        private (bool success, object? result) HandleRetryableFailure(object? resultObject)
        {
            lock (_lockObject)
            {
                if (_stateData.IsRetryLimitExceeded(Configuration.MaxRetries))
                {
                    // Retry limit exceeded - handle based on process state
                    return HandleRetryLimitExceeded(resultObject);
                }

                // Increment retry count and continue
                _stateData.IncrementRetryCount();
                return (true, resultObject); // Retry is considered a successful iteration
            }
        }

        /// <summary>
        /// Handles abort task failure.
        /// </summary>
        private (bool success, object? result) HandleAbortFailure(object? resultObject)
        {
            lock (_lockObject)
            {
                if (_stateData.ProcessState == MultiStepProcessState.Running)
                {
                    // Switch to rollback mode
                    _stateData.UpdateProcessState(MultiStepProcessState.RollingBack);
                    _stateData.ResetRetryCount();
                    return (true, resultObject); // Switching to rollback is considered successful
                }
                else
                {
                    // Already in rollback mode - process cannot continue
                    _stateData.MarkAsFinished(MultiStepProcessFailureReason.RollbackFailed);
                    _lastIterationFailed = true;
                    return (false, resultObject);
                }
            }
        }

        /// <summary>
        /// Handles timeout task failure based on configuration.
        /// </summary>
        private (bool success, object? result) HandleTimeoutFailure(object? resultObject)
        {
            // Handle timeout based on configuration
            if (Configuration.TimeoutBehavior == MultiStepProcessTaskRunResult.FailedRetry)
            {
                return HandleRetryableFailure(resultObject);
            }
            else
            {
                return HandleAbortFailure(resultObject);
            }
        }

        /// <summary>
        /// Handles retry limit exceeded scenario.
        /// </summary>
        private (bool success, object? result) HandleRetryLimitExceeded(object? resultObject)
        {
            // Create the retry exceeded exception for tracking
            var retryException = new MaxRetryExceededException(_stateData.CurrentState, _stateData.RetryCount, Configuration.MaxRetries);
            _lastException = retryException;

            if (_stateData.ProcessState == MultiStepProcessState.Running)
            {
                // Switch to rollback mode
                _stateData.UpdateProcessState(MultiStepProcessState.RollingBack);
                _stateData.ResetRetryCount();
                return (true, resultObject);
            }
            else
            {
                // Already in rollback mode - process cannot continue
                _stateData.MarkAsFinished(MultiStepProcessFailureReason.RetryLimitExceeded);
                _lastIterationFailed = true;
                return (false, resultObject);
            }
        }

        /// <summary>
        /// Gets detailed information about the current process state for debugging.
        /// </summary>
        public Dictionary<string, object> GetProcessInfo()
        {
            var info = new Dictionary<string, object>();

            if (_stateData != null)
            {
                info["CurrentState"] = _stateData.CurrentState?.ToString() ?? "null";
                info["ProcessState"] = _stateData.ProcessState.ToString();
                info["IsFinished"] = _stateData.IsFinished;
                info["RetryCount"] = _stateData.RetryCount;
                info["FailureReason"] = _stateData.FailureReason.ToString();
                info["CorrelationId"] = _stateData.CorrelationId ?? "null";
                info["CreatedAt"] = _stateData.CreatedAt;
                info["UpdatedAt"] = _stateData.UpdatedAt;
            }
            else
            {
                info["Status"] = "Not initialized";
            }

            info["LastIterationFailed"] = _lastIterationFailed;
            info["LastException"] = _lastException?.Message ?? "null";
            info["ConfiguredSteps"] = _stepDefinitions.Keys.Select(k => k.ToString()).ToArray();
            info["MaxRetries"] = Configuration.MaxRetries;
            info["TimeoutBehavior"] = Configuration.TimeoutBehavior.ToString();

            return info;
        }
    }
}
